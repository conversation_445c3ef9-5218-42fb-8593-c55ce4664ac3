import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import { generateId } from "@/lib/helpers";

export const candidate = sqliteTable("candidate", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => generateId()),
  fullName: text("full_name").notNull(),
  email: text("email").notNull(),
  phoneNumber: text("phone_number").notNull(),
  citizenship: text("citizenship").notNull(),
  resume: text("resume"), // File path or URL to resume
  coverLetter: text("cover_letter"), // Cover letter text
  additionalInfo: text("additional_info"), // Any additional information
  createdAt: integer("created_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: "timestamp" })
    .notNull()
    .$defaultFn(() => new Date())
    .$onUpdate(() => new Date()),
});

export const insertCandidateSchema = createInsertSchema(candidate);
export const selectCandidateSchema = createSelectSchema(candidate);
export const patchCandidateSchema = selectCandidateSchema
  .partial()
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

export type Candidate = typeof candidate.$inferSelect;
export type NewCandidate = typeof candidate.$inferInsert;
