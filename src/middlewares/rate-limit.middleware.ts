import { getRedisClient } from "@/lib/redis/config";
import type { Context, MiddlewareHandler } from "hono";
import { TOO_MANY_REQUESTS } from "stoker/http-status-codes";
import { logger } from "./pino-logger.middleware";

interface RateLimitOptions {
  max: number;
  window: `${number}${"s" | "m" | "h" | "d"}`;
  keyGenerator?: (c: Context) => string;
  onRateLimit?: (c: Context) => Response | Promise<Response>;
}

const parseWindow = (window: string): number => {
  const value = Number.parseInt(window);
  const unit = window.slice(-1);

  const multipliers: Record<string, number> = {
    s: 1,
    m: 60,
    h: 3600,
    d: 86400,
  };

  return value * multipliers[unit];
};

export const rateLimit = (options: RateLimitOptions): MiddlewareHandler => {
  return async (c, next) => {
    const redis = getRedisClient();

    try {
      const keyGenerator =
        options.keyGenerator ??
        ((c) =>
          c.req.raw.headers.get("x-forwarded-for") ||
          c.req.raw.headers.get("x-real-ip") ||
          "unknown");
      const key = `rate-limit:${keyGenerator(c)}`;
      const windowSeconds = parseWindow(options.window);

      const [[, count]] = (await redis
        .multi()
        .incr(key)
        .expire(key, windowSeconds)
        .exec())!;

      const remaining = Math.max(0, options.max - (count as number));

      c.header("X-RateLimit-Limit", options.max.toString());
      c.header("X-RateLimit-Remaining", remaining.toString());
      c.header(
        "X-RateLimit-Reset",
        (Math.floor(Date.now() / 1000) + windowSeconds).toString(),
      );

      if ((count as number) > options.max) {
        if (options.onRateLimit) {
          return options.onRateLimit(c);
        }
        return c.json({ message: "Rate limit exceeded" }, TOO_MANY_REQUESTS);
      }

      await next();
    } catch (error) {
      logger.error("Rate limit error:", error);
      await next();
    }
  };
};
