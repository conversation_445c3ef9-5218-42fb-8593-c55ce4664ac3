import { SUPPORTED_LANGUAGES } from "@/lib/constants";
import * as HttpStatusPhrases from "stoker/http-status-phrases";
import { createMessageObjectSchema } from "stoker/openapi/schemas";
import { z } from "zod";

export const timestampsSchema = z.object({
  createdAt: z.date().or(z.string().datetime()),
  updatedAt: z.date().or(z.string().datetime()),
  archivedAt: z.date().or(z.string().datetime()).nullable(),
});

export const paginationParamsSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().optional(),
  sortBy: z.enum(["createdAt", "updatedAt"]).default("createdAt"),
  sortDirection: z.enum(["asc", "desc"]).default("desc"),
});

export const translationSchema = z.object({
  language: z.enum(SUPPORTED_LANGUAGES),
  text: z.string().openapi({
    description: "Translated text",
  }),
});

export const translatedFieldSchema = z.record(
  z.enum(SUPPORTED_LANGUAGES),
  z.string(),
);

export function createPaginatedSchema<T extends z.ZodType>(itemSchema: T) {
  return z.object({
    items: z.array(itemSchema),
    total: z.number().int().min(0),
    page: z.number().int().min(1).optional(),
    limit: z.number().int().min(1).optional(),
    pages: z.number().int().min(0).optional(),
  });
}

export const notFoundSchema = createMessageObjectSchema(
  HttpStatusPhrases.NOT_FOUND,
);
export const unauthorizedSchema = createMessageObjectSchema(
  HttpStatusPhrases.UNAUTHORIZED,
);

export const forbiddenSchema = createMessageObjectSchema(
  HttpStatusPhrases.FORBIDDEN,
);

export const unprocessableEntitySchema = createMessageObjectSchema(
  HttpStatusPhrases.UNPROCESSABLE_ENTITY,
);

export const idParamsSchema = z.object({
  id: z.string().openapi({ description: "The id of the task" }),
});
