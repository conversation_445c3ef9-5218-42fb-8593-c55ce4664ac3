import { createAccessControl } from "better-auth/plugins/access";
import { adminAc, defaultStatements } from "better-auth/plugins/admin/access";

export const statements = {
  ...defaultStatements,
  jobVacancy: ["create", "read", "update", "delete", "list", "publish"],
  jobApplication: ["create", "read", "update", "delete", "list"],
  editor: ["create", "read", "update", "delete"],
  author: ["create", "read", "update", "delete"],
} as const;

export type Statements = typeof statements;
export type Permissions = {
  [K in keyof Statements]?: (typeof statements)[K][number][];
};

export const ac = createAccessControl(statements);

export const roles = {
  admin: ac.newRole({
    ...adminAc.statements,
    jobVacancy: ["create", "read", "update", "delete", "list", "publish"],
    jobApplication: ["create", "read", "update", "delete", "list"],
    editor: ["create", "read", "update", "delete"],
    author: ["create", "read", "update", "delete"],
  }),

  author: ac.newRole({
    jobVacancy: ["create", "read", "update", "list", "delete"],
    jobApplication: ["read", "list"],
    // user: ["list"],
  }),

  editor: ac.newRole({
    jobVacancy: ["create", "read", "update", "list", "publish", "delete"],
    jobApplication: ["read", "update", "delete", "list"],
    user: ["list"],
    author: ["read"],
  }),
} as const;

export const DEFAULT_ROLE = "author" as const;
