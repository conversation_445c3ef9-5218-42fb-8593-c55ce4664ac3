import { selectJobApplicationSchema, selectCandidateSchema } from "@/db/schema";
import { createPaginatedSchema, paginationParamsSchema } from "@/lib/schemas";
import { z } from "zod";

// DTO for creating job applications with candidate data
export const createJobApplicationDTO = z.object({
  jobVacancyId: z.string().min(1),
  notes: z.string().optional(),
  status: z.enum(["reviewed", "in_process", "closed"]).optional(),
  // Candidate data
  fullName: z.string().min(1),
  email: z.string().email(),
  phoneNumber: z.string().min(1),
  citizenship: z.string().min(1),
  resume: z.string().optional(),
  coverLetter: z.string().optional(),
  additionalInfo: z.string().optional(),
});

// Response DTO that includes candidate data
export const jobApplicationWithCandidateDTO = selectJobApplicationSchema.extend({
  candidate: selectCandidateSchema,
});

export const listApplicationsDTO = createPaginatedSchema(jobApplicationWithCandidateDTO);
export const listApplicationsQueryDTO = paginationParamsSchema.partial();

export type CreateJobApplicationDTO = z.infer<typeof createJobApplicationDTO>;
export type JobApplicationWithCandidate = z.infer<typeof jobApplicationWithCandidateDTO>;
