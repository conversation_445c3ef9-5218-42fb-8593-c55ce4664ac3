import type { NewCandidate } from "@/db/schema";

import db from "@/db";
import { candidate } from "@/db/schema";
import { count, eq, or } from "drizzle-orm";

export class CandidateOperations {
  static async findMany(params?: {
    page?: number;
    limit?: number;
    sortBy?: "createdAt" | "updatedAt";
    sortDirection?: "asc" | "desc";
  }) {
    const {
      page,
      limit,
      sortBy = "createdAt",
      sortDirection = "desc",
    } = params ?? {};

    const [candidates, [{ total }]] = await Promise.all([
      db.query.candidate.findMany({
        limit,
        offset: limit && page ? (page - 1) * limit : undefined,
        orderBy: {
          [sortBy]: sortDirection,
        },
      }),
      db
        .select({
          total: count(),
        })
        .from(candidate)
        .limit(1),
    ]);

    return {
      total,
      page,
      limit,
      pages: limit ? Math.ceil(total / limit) : undefined,
      items: candidates,
    };
  }

  static async findById(id: string) {
    return db.query.candidate.findFirst({
      where: { id },
    });
  }

  static async findByIdWithApplications(id: string) {
    return db.query.candidate.findFirst({
      where: { id },
      with: {
        jobApplications: {
          with: {
            jobVacancy: true,
          },
        },
      },
    });
  }

  /**
   * Smart candidate matching - first by phone number, then by email
   * This helps identify returning candidates
   */
  static async findByContactInfo(phoneNumber: string, email: string) {
    // First try to find by phone number (most reliable)
    let existingCandidate = await db.query.candidate.findFirst({
      where: eq(candidate.phoneNumber, phoneNumber),
    });

    // If not found by phone, try by email
    if (!existingCandidate) {
      existingCandidate = await db.query.candidate.findFirst({
        where: eq(candidate.email, email),
      });
    }

    return existingCandidate;
  }

  /**
   * Find or create a candidate based on contact information
   * This is the main method for handling job applications
   */
  static async findOrCreate(candidateData: NewCandidate) {
    const existingCandidate = await this.findByContactInfo(
      candidateData.phoneNumber,
      candidateData.email
    );

    if (existingCandidate) {
      // Update existing candidate with any new information
      const updatedCandidate = await this.update(existingCandidate.id, {
        fullName: candidateData.fullName,
        email: candidateData.email,
        phoneNumber: candidateData.phoneNumber,
        citizenship: candidateData.citizenship,
        resume: candidateData.resume || existingCandidate.resume,
        coverLetter: candidateData.coverLetter || existingCandidate.coverLetter,
        additionalInfo: candidateData.additionalInfo || existingCandidate.additionalInfo,
      });
      return updatedCandidate;
    }

    // Create new candidate
    return this.create(candidateData);
  }

  static async create(data: NewCandidate) {
    const [newCandidate] = await db
      .insert(candidate)
      .values(data)
      .returning();
    return newCandidate;
  }

  static async update(id: string, updates: Partial<NewCandidate>) {
    const [updated] = await db
      .update(candidate)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(candidate.id, id))
      .returning();
    return updated;
  }

  static async delete(id: string) {
    const [deleted] = await db
      .delete(candidate)
      .where(eq(candidate.id, id))
      .returning();
    return !!deleted;
  }
}
